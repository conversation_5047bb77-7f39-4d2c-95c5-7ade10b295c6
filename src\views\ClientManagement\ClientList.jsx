import { useState, useEffect } from 'react';

// material-ui
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import { IconUserPlus, IconSearch, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';

// Bootstrap components
import { Modal, Form, Row, Col, Badge, Spinner, Button as BootstrapButton } from 'react-bootstrap';
import { FaEye, FaEdit, FaTrashAlt } from 'react-icons/fa';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { fetchClients, updateClient, deleteClient } from '../../services/clientService';
import 'ui-component/extended/ProfessionalModal.css';

// assets

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [isLoading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState([]);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Edit form state
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'regular',
    partner_type: '',
    company: '',
    location: 'Tunis',
    seller_group: '',
    seller_brand: ''
  });

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching

    if (term === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(
        (client) =>
          client.name?.toLowerCase().includes(term.toLowerCase()) ||
          client.email?.toLowerCase().includes(term.toLowerCase()) ||
          client.phone?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentClients = filteredClients.slice(startIndex, endIndex);

  // Handle pagination
  const handlePageChange = (event, pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (event) => {
    setItemsPerPage(event.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchClients();
      setClients(data || []);
      setFilteredClients(data || []);
    } catch (err) {
      console.error('Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
  }, []);

  // Modal handlers
  const handleViewClient = (client) => {
    setSelectedClient(client);
    setViewModalOpen(true);
  };

  const handleEditClient = (client) => {
    setSelectedClient(client);
    setEditFormData({
      name: client.name || '',
      email: client.email || '',
      phone: client.phone || '',
      category: client.category || 'regular',
      partner_type: client.partner_type || '',
      company: client.company || '',
      location: client.location || 'Tunis',
      seller_group: client.seller_group || '',
      seller_brand: client.seller_brand || ''
    });
    setEditModalOpen(true);
  };

  const handleDeleteClient = (client) => {
    setSelectedClient(client);
    setDeleteModalOpen(true);
  };

  const handleEditFormChange = (field, value) => {
    setEditFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditSubmit = async () => {
    if (!selectedClient) return;

    setActionLoading(true);
    try {
      await updateClient(selectedClient.id, editFormData);
      setEditModalOpen(false);
      setSelectedClient(null);
      await loadClients(); // Reload the list
    } catch (err) {
      console.error('Error updating client:', err);
      setError('Erreur lors de la modification du client: ' + err.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedClient) return;

    setActionLoading(true);
    try {
      await deleteClient(selectedClient.id);
      setDeleteModalOpen(false);
      setSelectedClient(null);
      await loadClients(); // Reload the list
    } catch (err) {
      console.error('Error deleting client:', err);
      setError('Erreur lors de la suppression du client: ' + err.message);
    } finally {
      setActionLoading(false);
    }
  };

  const closeAllModals = () => {
    setViewModalOpen(false);
    setEditModalOpen(false);
    setDeleteModalOpen(false);
    setSelectedClient(null);
    setActionLoading(false);
  };

  const renderClientTable = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!filteredClients || filteredClients.length === 0) {
      return (
        <Typography variant="body1" sx={{ p: 2, textAlign: 'center' }}>
          Aucun client trouvé.
        </Typography>
      );
    }

    return (
      <Box>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nom</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Téléphone</TableCell>
                <TableCell>Date d'inscription</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentClients.map((client) => (
                <TableRow key={client.id}>
                  <TableCell>{client.name || client.first_name + ' ' + client.last_name || 'N/A'}</TableCell>
                  <TableCell>{client.email || 'N/A'}</TableCell>
                  <TableCell>{client.phone || 'N/A'}</TableCell>
                  <TableCell>{client.created_at ? new Date(client.created_at).toLocaleDateString('fr-FR') : 'N/A'}</TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary" title="Voir" onClick={() => handleViewClient(client)}>
                      <IconEye />
                    </IconButton>
                    <IconButton size="small" color="secondary" title="Modifier" onClick={() => handleEditClient(client)}>
                      <IconEdit />
                    </IconButton>
                    <IconButton size="small" color="error" title="Supprimer" onClick={() => handleDeleteClient(client)}>
                      <IconTrash />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Affichage de {startIndex + 1} à {Math.min(endIndex, filteredClients.length)} sur {filteredClients.length} client(s)
              </Typography>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Par page</InputLabel>
                <Select value={itemsPerPage} label="Par page" onChange={handleItemsPerPageChange}>
                  <MenuItem value={5}>5 par page</MenuItem>
                  <MenuItem value={10}>10 par page</MenuItem>
                  <MenuItem value={25}>25 par page</MenuItem>
                  <MenuItem value={50}>50 par page</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" showFirstButton showLastButton />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <MainCard
      title="Client Management"
      secondary={
        <Button variant="contained" startIcon={<IconUserPlus />}>
          Add Client
        </Button>
      }
    >
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher des clients..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconSearch />
                </InputAdornment>
              )
            }}
          />
        </Box>

        <Typography variant="h5" gutterBottom sx={{ mt: 2 }}>
          Tous les Clients ({filteredClients.length})
        </Typography>
        {renderClientTable()}
      </Box>

      {/* View Client Modal */}
      <Modal
        show={viewModalOpen}
        onHide={closeAllModals}
        size="lg"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-info text-white">
          <Modal.Title>
            <FaEye className="me-2" />
            Détails du client
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          {selectedClient && (
            <Row>
              <Col md={6}>
                <div className="mb-3">
                  <strong className="text-muted">Nom:</strong>
                  <div>{selectedClient.name || selectedClient.first_name + ' ' + selectedClient.last_name || 'N/A'}</div>
                </div>
                <div className="mb-3">
                  <strong className="text-muted">Email:</strong>
                  <div>{selectedClient.email || 'N/A'}</div>
                </div>
                <div className="mb-3">
                  <strong className="text-muted">Téléphone:</strong>
                  <div>{selectedClient.phone || 'N/A'}</div>
                </div>
              </Col>
              <Col md={6}>
                <div className="mb-3">
                  <strong className="text-muted">Date d'inscription:</strong>
                  <div>{selectedClient.created_at ? new Date(selectedClient.created_at).toLocaleDateString('fr-FR') : 'N/A'}</div>
                </div>
                <div className="mb-3">
                  <strong className="text-muted">Catégorie:</strong>
                  <div>
                    <Badge bg="primary">{selectedClient.category || 'Regular'}</Badge>
                  </div>
                </div>
                <div className="mb-3">
                  <strong className="text-muted">Entreprise:</strong>
                  <div>{selectedClient.company || 'N/A'}</div>
                </div>
              </Col>
            </Row>
          )}
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <BootstrapButton variant="secondary" onClick={closeAllModals}>
            Fermer
          </BootstrapButton>
        </Modal.Footer>
      </Modal>

      {/* Edit Client Modal */}
      <Modal
        show={editModalOpen}
        onHide={closeAllModals}
        size="lg"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-primary text-white">
          <Modal.Title>
            <FaEdit className="me-2" />
            Modifier le client
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Nom</Form.Label>
                  <Form.Control
                    type="text"
                    value={editFormData.name}
                    onChange={(e) => handleEditFormChange('name', e.target.value)}
                    placeholder="Nom du client"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => handleEditFormChange('email', e.target.value)}
                    placeholder="Email du client"
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Téléphone</Form.Label>
                  <Form.Control
                    type="text"
                    value={editFormData.phone}
                    onChange={(e) => handleEditFormChange('phone', e.target.value)}
                    placeholder="Téléphone du client"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Catégorie</Form.Label>
                  <Form.Select value={editFormData.category} onChange={(e) => handleEditFormChange('category', e.target.value)}>
                    <option value="regular">Regular</option>
                    <option value="premium">Premium</option>
                    <option value="vip">VIP</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Entreprise</Form.Label>
                  <Form.Control
                    type="text"
                    value={editFormData.company}
                    onChange={(e) => handleEditFormChange('company', e.target.value)}
                    placeholder="Entreprise du client"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Localisation</Form.Label>
                  <Form.Control
                    type="text"
                    value={editFormData.location}
                    onChange={(e) => handleEditFormChange('location', e.target.value)}
                    placeholder="Localisation du client"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <BootstrapButton variant="secondary" onClick={closeAllModals} disabled={actionLoading}>
            Annuler
          </BootstrapButton>
          <BootstrapButton variant="primary" onClick={handleEditSubmit} disabled={actionLoading}>
            {actionLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Enregistrement...
              </>
            ) : (
              'Sauvegarder'
            )}
          </BootstrapButton>
        </Modal.Footer>
      </Modal>

      {/* Delete Client Modal */}
      <Modal
        show={deleteModalOpen}
        onHide={closeAllModals}
        size="sm"
        className="professional-modal"
        dialogClassName="professional-modal-dialog"
      >
        <Modal.Header closeButton className="professional-modal-header bg-danger text-white">
          <Modal.Title>
            <FaTrashAlt className="me-2" />
            Confirmer la suppression
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="professional-modal-body">
          <div className="text-center py-3">
            <p className="mb-2">
              Êtes-vous sûr de vouloir supprimer le client{' '}
              <strong>{selectedClient?.name || selectedClient?.first_name + ' ' + selectedClient?.last_name || 'ce client'}</strong> ?
            </p>
            <small className="text-muted">Cette action est irréversible.</small>
          </div>
        </Modal.Body>
        <Modal.Footer className="professional-modal-footer">
          <BootstrapButton variant="secondary" onClick={closeAllModals} disabled={actionLoading}>
            Annuler
          </BootstrapButton>
          <BootstrapButton variant="danger" onClick={handleDeleteConfirm} disabled={actionLoading}>
            {actionLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Suppression...
              </>
            ) : (
              'Supprimer'
            )}
          </BootstrapButton>
        </Modal.Footer>
      </Modal>
    </MainCard>
  );
};

export default ClientList;
