import { useState, useEffect } from 'react';

// material-ui
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import { IconUserPlus, IconSearch, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { fetchClients } from '../../services/clientService';

// assets

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [isLoading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState([]);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching

    if (term === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(
        (client) =>
          client.name?.toLowerCase().includes(term.toLowerCase()) ||
          client.email?.toLowerCase().includes(term.toLowerCase()) ||
          client.phone?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentClients = filteredClients.slice(startIndex, endIndex);

  // Handle pagination
  const handlePageChange = (event, pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (event) => {
    setItemsPerPage(event.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchClients();
      setClients(data || []);
      setFilteredClients(data || []);
    } catch (err) {
      console.error('Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
  }, []);

  const renderClientTable = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!filteredClients || filteredClients.length === 0) {
      return (
        <Typography variant="body1" sx={{ p: 2, textAlign: 'center' }}>
          Aucun client trouvé.
        </Typography>
      );
    }

    return (
      <Box>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nom</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Téléphone</TableCell>
                <TableCell>Date d'inscription</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentClients.map((client) => (
                <TableRow key={client.id}>
                  <TableCell>{client.name || client.first_name + ' ' + client.last_name || 'N/A'}</TableCell>
                  <TableCell>{client.email || 'N/A'}</TableCell>
                  <TableCell>{client.phone || 'N/A'}</TableCell>
                  <TableCell>{client.created_at ? new Date(client.created_at).toLocaleDateString('fr-FR') : 'N/A'}</TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary" title="Voir">
                      <IconEye />
                    </IconButton>
                    <IconButton size="small" color="secondary" title="Modifier">
                      <IconEdit />
                    </IconButton>
                    <IconButton size="small" color="error" title="Supprimer">
                      <IconTrash />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Affichage de {startIndex + 1} à {Math.min(endIndex, filteredClients.length)} sur {filteredClients.length} client(s)
              </Typography>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Par page</InputLabel>
                <Select value={itemsPerPage} label="Par page" onChange={handleItemsPerPageChange}>
                  <MenuItem value={5}>5 par page</MenuItem>
                  <MenuItem value={10}>10 par page</MenuItem>
                  <MenuItem value={25}>25 par page</MenuItem>
                  <MenuItem value={50}>50 par page</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" showFirstButton showLastButton />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <MainCard
      title="Client Management"
      secondary={
        <Button variant="contained" startIcon={<IconUserPlus />}>
          Add Client
        </Button>
      }
    >
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher des clients..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconSearch />
                </InputAdornment>
              )
            }}
          />
        </Box>

        <Typography variant="h5" gutterBottom sx={{ mt: 2 }}>
          Tous les Clients ({filteredClients.length})
        </Typography>
        {renderClientTable()}
      </Box>
    </MainCard>
  );
};

export default ClientList;
