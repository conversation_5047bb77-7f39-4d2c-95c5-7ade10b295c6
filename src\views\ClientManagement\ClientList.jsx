import { useState, useEffect } from 'react';

// material-ui
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  IconButton,
  TextField,
  InputAdornment,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Chip
} from '@mui/material';
import { IconUserPlus, IconSearch, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { fetchClients, updateClient, deleteClient } from '../../services/clientService';

// assets

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [isLoading, setLoading] = useState(true);
  const [clients, setClients] = useState([]);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState([]);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Edit form state
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'regular',
    partner_type: '',
    company: '',
    location: 'Tunis',
    seller_group: '',
    seller_brand: ''
  });

  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching

    if (term === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(
        (client) =>
          client.name?.toLowerCase().includes(term.toLowerCase()) ||
          client.email?.toLowerCase().includes(term.toLowerCase()) ||
          client.phone?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentClients = filteredClients.slice(startIndex, endIndex);

  // Handle pagination
  const handlePageChange = (event, pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (event) => {
    setItemsPerPage(event.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchClients();
      setClients(data || []);
      setFilteredClients(data || []);
    } catch (err) {
      console.error('Error loading clients:', err);
      setError('Erreur lors du chargement des clients: ' + err.message);
      setClients([]);
      setFilteredClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
  }, []);

  // Modal handlers
  const handleViewClient = (client) => {
    setSelectedClient(client);
    setViewModalOpen(true);
  };

  const handleEditClient = (client) => {
    setSelectedClient(client);
    setEditFormData({
      name: client.name || '',
      email: client.email || '',
      phone: client.phone || '',
      category: client.category || 'regular',
      partner_type: client.partner_type || '',
      company: client.company || '',
      location: client.location || 'Tunis',
      seller_group: client.seller_group || '',
      seller_brand: client.seller_brand || ''
    });
    setEditModalOpen(true);
  };

  const handleDeleteClient = (client) => {
    setSelectedClient(client);
    setDeleteModalOpen(true);
  };

  const handleEditFormChange = (field, value) => {
    setEditFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditSubmit = async () => {
    if (!selectedClient) return;

    setActionLoading(true);
    try {
      await updateClient(selectedClient.id, editFormData);
      setEditModalOpen(false);
      setSelectedClient(null);
      await loadClients(); // Reload the list
    } catch (err) {
      console.error('Error updating client:', err);
      setError('Erreur lors de la modification du client: ' + err.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedClient) return;

    setActionLoading(true);
    try {
      await deleteClient(selectedClient.id);
      setDeleteModalOpen(false);
      setSelectedClient(null);
      await loadClients(); // Reload the list
    } catch (err) {
      console.error('Error deleting client:', err);
      setError('Erreur lors de la suppression du client: ' + err.message);
    } finally {
      setActionLoading(false);
    }
  };

  const closeAllModals = () => {
    setViewModalOpen(false);
    setEditModalOpen(false);
    setDeleteModalOpen(false);
    setSelectedClient(null);
    setActionLoading(false);
  };

  const renderClientTable = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={3}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!filteredClients || filteredClients.length === 0) {
      return (
        <Typography variant="body1" sx={{ p: 2, textAlign: 'center' }}>
          Aucun client trouvé.
        </Typography>
      );
    }

    return (
      <Box>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nom</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Téléphone</TableCell>
                <TableCell>Date d'inscription</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentClients.map((client) => (
                <TableRow key={client.id}>
                  <TableCell>{client.name || client.first_name + ' ' + client.last_name || 'N/A'}</TableCell>
                  <TableCell>{client.email || 'N/A'}</TableCell>
                  <TableCell>{client.phone || 'N/A'}</TableCell>
                  <TableCell>{client.created_at ? new Date(client.created_at).toLocaleDateString('fr-FR') : 'N/A'}</TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary" title="Voir" onClick={() => handleViewClient(client)}>
                      <IconEye />
                    </IconButton>
                    <IconButton size="small" color="secondary" title="Modifier" onClick={() => handleEditClient(client)}>
                      <IconEdit />
                    </IconButton>
                    <IconButton size="small" color="error" title="Supprimer" onClick={() => handleDeleteClient(client)}>
                      <IconTrash />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Affichage de {startIndex + 1} à {Math.min(endIndex, filteredClients.length)} sur {filteredClients.length} client(s)
              </Typography>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Par page</InputLabel>
                <Select value={itemsPerPage} label="Par page" onChange={handleItemsPerPageChange}>
                  <MenuItem value={5}>5 par page</MenuItem>
                  <MenuItem value={10}>10 par page</MenuItem>
                  <MenuItem value={25}>25 par page</MenuItem>
                  <MenuItem value={50}>50 par page</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" showFirstButton showLastButton />
          </Box>
        )}
      </Box>
    );
  };

  return (
    <MainCard
      title="Client Management"
      secondary={
        <Button variant="contained" startIcon={<IconUserPlus />}>
          Add Client
        </Button>
      }
    >
      <Box sx={{ width: '100%' }}>
        {/* Search Field */}
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Rechercher des clients..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconSearch />
                </InputAdornment>
              )
            }}
          />
        </Box>

        <Typography variant="h5" gutterBottom sx={{ mt: 2 }}>
          Tous les Clients ({filteredClients.length})
        </Typography>
        {renderClientTable()}
      </Box>

      {/* View Client Modal */}
      <Dialog open={viewModalOpen} onClose={closeAllModals} maxWidth="md" fullWidth>
        <DialogTitle>Détails du client</DialogTitle>
        <DialogContent>
          {selectedClient && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Nom
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedClient.name || selectedClient.first_name + ' ' + selectedClient.last_name || 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Email
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedClient.email || 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Téléphone
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedClient.phone || 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Date d'inscription
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedClient.created_at ? new Date(selectedClient.created_at).toLocaleDateString('fr-FR') : 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Catégorie
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  <Chip label={selectedClient.category || 'Regular'} size="small" />
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Entreprise
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedClient.company || 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={closeAllModals}>Fermer</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Client Modal */}
      <Dialog open={editModalOpen} onClose={closeAllModals} maxWidth="md" fullWidth>
        <DialogTitle>Modifier le client</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nom"
                value={editFormData.name}
                onChange={(e) => handleEditFormChange('name', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={editFormData.email}
                onChange={(e) => handleEditFormChange('email', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Téléphone"
                value={editFormData.phone}
                onChange={(e) => handleEditFormChange('phone', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Catégorie</InputLabel>
                <Select value={editFormData.category} onChange={(e) => handleEditFormChange('category', e.target.value)} label="Catégorie">
                  <MenuItem value="regular">Regular</MenuItem>
                  <MenuItem value="premium">Premium</MenuItem>
                  <MenuItem value="vip">VIP</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Entreprise"
                value={editFormData.company}
                onChange={(e) => handleEditFormChange('company', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Localisation"
                value={editFormData.location}
                onChange={(e) => handleEditFormChange('location', e.target.value)}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeAllModals} disabled={actionLoading}>
            Annuler
          </Button>
          <Button onClick={handleEditSubmit} variant="contained" disabled={actionLoading}>
            {actionLoading ? <CircularProgress size={20} /> : 'Sauvegarder'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Client Modal */}
      <Dialog open={deleteModalOpen} onClose={closeAllModals} maxWidth="sm" fullWidth>
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer le client{' '}
            <strong>{selectedClient?.name || selectedClient?.first_name + ' ' + selectedClient?.last_name || 'ce client'}</strong> ?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeAllModals} disabled={actionLoading}>
            Annuler
          </Button>
          <Button onClick={handleDeleteConfirm} variant="contained" color="error" disabled={actionLoading}>
            {actionLoading ? <CircularProgress size={20} /> : 'Supprimer'}
          </Button>
        </DialogActions>
      </Dialog>
    </MainCard>
  );
};

export default ClientList;
